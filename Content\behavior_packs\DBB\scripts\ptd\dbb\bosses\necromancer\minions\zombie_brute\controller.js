import { executeSplashAttack } from "./attacks/splash";
import { getTarget } from "../../../general_mechanics/targetUtils";
import { getDistance } from "../../../../utilities/vector3";
/**
 * Attack duration in ticks (20 ticks per second)
 * The total duration of the animation
 */
export const ATTACK_DURATION = 89; // 4.45 seconds for the full attack animation
/**
 * Attack timing point in ticks
 * When the damage and effects should be applied
 */
export const ATTACK_TIMING = 35; // Apply damage at tick 35
/**
 * Attack range for splash attack
 * The optimal distance for triggering the attack
 */
export const ATTACK_RANGE = 3.5; // Trigger attack when within 3.5 blocks of target
/**
 * Maximum distance allowed during attack animation
 * If target moves beyond this distance, attack will be canceled
 */
export const MAX_ATTACK_DISTANCE = 6.0; // Cancel attack if target moves too far
/**
 * Selects and executes attacks for the Zombie Brute based on target distance
 * Will trigger a splash attack when within range of a target
 *
 * @param zombieBrute The zombie brute entity
 * @returns Whether an attack was initiated
 */
export function selectAttack(zombieBrute) {
    // Skip if entity is already attacking or on cooldown
    const attack = zombieBrute.getProperty("ptd_dbb:attack");
    const attackCooldown = zombieBrute.getProperty("ptd_dbb:attack_cooldown");
    if (attack !== "none" || attackCooldown > 0) {
        return false;
    }
    // Find the nearest valid target
    const target = getTarget(zombieBrute, zombieBrute.location, 32, ["zombie_brute", "necromancer"]);
    if (!target) {
        return false;
    }
    // Calculate distance to target
    const distance = getDistance(zombieBrute.location, target.location);
    // If within range, trigger splash attack
    if (distance <= ATTACK_RANGE) {
        zombieBrute.triggerEvent("ptd_dbb:splash_attack");
        return true;
    }
    return false;
}
/**
 * Handles attack logic based on attack type and timer
 * Checks target distance every tick and cancels attack if target moves out of range
 *
 * @param zombieBrute The zombie brute entity
 */
export function handleAttackLogic(zombieBrute) {
    const attack = zombieBrute.getProperty("ptd_dbb:attack");
    // Add max slowness throughout the attack length
    zombieBrute.addEffect("minecraft:slowness", 3, { amplifier: 255, showParticles: false });
    if (attack === "none") {
        return;
    }
    // Get and increment attack timer
    let attackTimer = zombieBrute.getProperty("ptd_dbb:attack_timer");
    attackTimer = Math.min(attackTimer + 1, ATTACK_DURATION);
    zombieBrute.setProperty("ptd_dbb:attack_timer", attackTimer);
    // Check if target is still in range on every tick during the attack
    if (attack === "splash") {
        const target = getTarget(zombieBrute, zombieBrute.location, 32, ["necromancer"]);
        // If target is lost or moved too far away, cancel the attack
        if (!target || getDistance(zombieBrute.location, target.location) > MAX_ATTACK_DISTANCE) {
            zombieBrute.triggerEvent("ptd_dbb:reset_attack");
            return;
        }
        // Execute splash attack at the correct timing if target is still in range
        if (attackTimer === ATTACK_TIMING) {
            executeSplashAttack(zombieBrute);
        }
    }
    // Reset attack when animation completes
    if (attackTimer >= ATTACK_DURATION) {
        zombieBrute.triggerEvent("ptd_dbb:reset_attack");
    }
}
