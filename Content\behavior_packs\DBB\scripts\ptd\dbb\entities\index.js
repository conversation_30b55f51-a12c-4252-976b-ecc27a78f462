import { world } from "@minecraft/server";
import { handleLootMechanics } from "./lootMechanics";
import { zombieBruteMechanics } from "../bosses/necromancer/minions/zombie_brute/index";
import { grimhowlMechanics } from "../bosses/grimhowl/index";
export const customEntityIds = new Set([
    "ptd_dbb:winged_zombie",
    "ptd_dbb:zombie_brute",
    "ptd_dbb:skeleton_soul"
]);
export function deathMechanics(entity) {
    if (!entity)
        return;
    const deathProp = entity.getProperty("ptd_dbb:dead");
    if (deathProp) {
        handleLootMechanics(entity);
    }
    return;
}
export function handleEntityTick(entity) {
    if (!entity)
        return;
    const entityTypeId = entity.typeId;
    // Handle entity-specific behaviors
    switch (entityTypeId) {
        case "ptd_dbb:zombie_brute":
            zombieBruteMechanics(entity);
            break;
        default:
            break;
    }
}
// Global entity event listeners
world.afterEvents.entityHurt.subscribe((event) => {
    if (event.hurtEntity.typeId !== "ptd_dbb:grimhowl")
        return;
    grimhowlMechanics(event, "hurt");
});
world.afterEvents.entityLoad.subscribe((event) => {
    if (event.entity.typeId !== "ptd_dbb:grimhowl")
        return;
    grimhowlMechanics(event, "load");
});
