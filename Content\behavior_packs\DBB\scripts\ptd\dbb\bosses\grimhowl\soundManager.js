/**
 * List of all sound effects for the Grimhowl
 */
export const GRIMHOWL_SOUNDS = [
    "mob.ptd_dbb_grimhowl.sword_swipe",
    "mob.ptd_dbb_grimhowl.pre_claw",
    "mob.ptd_dbb_grimhowl.claw_attack",
    "mob.ptd_dbb_grimhowl.pre_pounce",
    "mob.ptd_dbb_grimhowl.pounce_attack",
    "mob.ptd_dbb_grimhowl.pounce_attack_swordless",
    "mob.ptd_dbb_grimhowl.spawn",
    "mob.ptd_dbb_grimhowl.roar",
    "mob.ptd_dbb_grimhowl.dodge",
    "mob.ptd_dbb_grimhowl.grunt"
];
/**
 * Stops all Grimhowl sound effects except the excluded one
 * Uses the stopsound command for more effective sound stopping
 *
 * @param entity The entity to stop sounds for
 * @param excludedSound Optional sound to exclude from stopping
 */
export function stopGrimhowlSounds(entity, excludedSound) {
    try {
        if (!entity)
            return;
        // Use the stopsound command to stop all sounds except the excluded one
        for (const sound of GRIMHOWL_SOUNDS) {
            // Skip the excluded sound
            if (excludedSound && sound === excludedSound)
                continue;
            // Use the stopsound command to stop the sound
            try {
                entity.runCommand(`stopsound @a ${sound}`);
            }
            catch (cmdError) {
                // If the command fails, log the error but continue with other sounds
                console.warn(`Error running stopsound command for ${sound}: ${cmdError}`);
            }
        }
    }
    catch (error) {
        console.warn(`Error stopping Grimhowl sounds: ${error}`);
    }
}
/**
 * Maps attack names to their corresponding sound effects
 * Used to play the appropriate sound for each attack type
 */
export const GRIMHOWL_ATTACK_SOUND_MAP = {
    leftClaw: "mob.ptd_dbb_grimhowl.claw_attack",
    rightClaw: "mob.ptd_dbb_grimhowl.claw_attack",
    spinningSlash: "mob.ptd_dbb_grimhowl.sword_swipe",
    backstep: "mob.ptd_dbb_grimhowl.dodge",
    roar: "mob.ptd_dbb_grimhowl.roar",
    slash: "mob.ptd_dbb_grimhowl.sword_swipe",
    pounce: "mob.ptd_dbb_grimhowl.pounce_attack",
    pounce_swordless: "mob.ptd_dbb_grimhowl.pounce_attack_swordless",
    shadowOnslaught: "mob.ptd_dbb_grimhowl.roar"
};
