{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "ptd_dbb:piglin_champion", "min_engine_version": "1.16.0", "materials": {"default": "entity_alphatest"}, "textures": {"default": "textures/ptd/dbb/entity/bosses/piglin_champion/default"}, "geometry": {"default": "geometry.ptd_dbb_piglin_champion"}, "animations": {"particle_effects": "animation.ptd_dbb_piglin_champion.particle_effects", "puke_scale_down": "animation.ptd_dbb_piglin_champion.puke_scale_down", "look_at_target": "animation.common.look_at_target", "spawn": "animation.ptd_dbb_piglin_champion.spawn", "idle": "animation.ptd_dbb_piglin_champion.idle", "walk": "animation.ptd_dbb_piglin_champion.walk", "death": "animation.ptd_dbb_piglin_champion.death", "charge": "animation.ptd_dbb_piglin_champion.charge", "charge_3": "animation.ptd_dbb_piglin_champion.charge_3", "charge_4": "animation.ptd_dbb_piglin_champion.charge_4", "charge_2": "animation.ptd_dbb_piglin_champion.charge_2", "charge_1": "animation.ptd_dbb_piglin_champion.charge_1", "charging_run": "animation.ptd_dbb_piglin_champion.charging_run", "vertical_attack": "animation.ptd_dbb_piglin_champion.vertical_attack", "horizontal_attack": "animation.ptd_dbb_piglin_champion.horizontal_attack", "healing": "animation.ptd_dbb_piglin_champion.healing", "spin_slam": "animation.ptd_dbb_piglin_champion.spin_slam", "damage_to_stunned": "animation.ptd_dbb_piglin_champion.damaged_to_stunned", "stunned_standing": "animation.ptd_dbb_piglin_champion.stunned_standing", "stunned_to_idle": "animation.ptd_dbb_piglin_champion.stunned_to_idle", "summoning_chant": "animation.ptd_dbb_piglin_champion.summoning_chant", "foot_stomp": "animation.ptd_dbb_piglin_champion.foot_stomp", "upchuck": "animation.ptd_dbb_piglin_champion.upchuck", "body_slam": "animation.ptd_dbb_piglin_champion.body_slam", "stunned_sitting": "animation.ptd_dbb_piglin_champion.stunned_sitting", "stunned_sitting_to_idle": "animation.ptd_dbb_piglin_champion.stunned_sitting_to_idle", "no_effects": "animation.ptd_dbb_piglin_champion.no_effects", "general": "controller.animation.ptd_dbb_piglin_champion.general", "stun": "controller.animation.ptd_dbb_piglin_champion.stun"}, "scripts": {"should_update_bones_and_effects_offscreen": true, "animate": ["general", "particle_effects", {"look_at_target": "q.property('ptd_dbb:spawning') == false && q.property('ptd_dbb:dead') == false && !(q.property('ptd_dbb:attack') == 'charging' && q.property('ptd_dbb:attack_timer') >= 133 && q.property('ptd_dbb:attack_timer') <= 253) && q.property('ptd_dbb:attack') != 'stunned_standing' && q.property('ptd_dbb:attack') != 'stunned_sitting'"}, {"puke_scale_down": "q.property('ptd_dbb:attack') != 'upchuck' || (q.property('ptd_dbb:attack') == 'upchuck' && q.property('ptd_dbb:attack_timer') <= 40)"}]}, "render_controllers": ["controller.render.ptd_dbb_default"], "particle_effects": {"pg_axe1_01": "ptd_dbb:pg_axe1_01", "pg_axe2_01": "ptd_dbb:pg_axe2_01", "pg_body_slam1_01": "ptd_dbb:pg_body_slam1_01", "pg_burp1_01": "ptd_dbb:pg_burp1_01", "pg_burp1_02": "ptd_dbb:pg_burp1_02", "pg_burp1_03": "ptd_dbb:pg_burp1_03", "pg_charge1_01": "ptd_dbb:pg_charge1_01", "pg_charge1_02": "ptd_dbb:pg_charge1_02", "pg_charge1_03": "ptd_dbb:pg_charge1_03", "pg_die1_01": "ptd_dbb:pg_die1_01", "pg_die2_01": "ptd_dbb:pg_die2_01", "pg_foot_stomp1_01": "ptd_dbb:pg_foot_stomp1_01", "pg_heal1_01": "ptd_dbb:pg_heal1_01", "pg_heal2_01": "ptd_dbb:pg_heal2_01", "pg_spawn1_01": "ptd_dbb:pg_spawn1_01", "pg_spawn2_01": "ptd_dbb:pg_spawn2_01", "pg_spinslam1_01": "ptd_dbb:pg_spinslam1_01", "pg_spinslam2_01": "ptd_dbb:pg_spinslam2_01", "pg_spinslam3_01": "ptd_dbb:pg_spinslam3_01", "pg_spinslam4_01": "ptd_dbb:pg_spinslam4_01", "pg_summon1_01": "ptd_dbb:pg_summon1_01", "pg_summon3_01": "ptd_dbb:pg_summon3_01", "pg_upchuck1_01": "ptd_dbb:pg_upchuck1_01"}, "sound_effects": {"spawn": "mob.ptd_dbb_piglin_champion.spawn", "charging": "mob.ptd_dbb_piglin_champion.charging", "vertical_attack": "mob.ptd_dbb_piglin_champion.vertical_attack", "horizontal_attack": "mob.ptd_dbb_piglin_champion.horizontal_attack", "healing": "mob.ptd_dbb_piglin_champion.healing", "spin_slam": "mob.ptd_dbb_piglin_champion.spin_slam", "damaged_to_stunned": "mob.ptd_dbb_piglin_champion.damaged_to_stunned", "stunned_standing": "mob.ptd_dbb_piglin_champion.stunned_standing", "stunned_to_idle": "mob.ptd_dbb_piglin_champion.stunned_to_idle", "summoning_chant": "mob.ptd_dbb_piglin_champion.summoning_chant", "foot_stomp": "mob.ptd_dbb_piglin_champion.foot_stomp", "upchuck": "mob.ptd_dbb_piglin_champion.upchuck", "body_slam": "mob.ptd_dbb_piglin_champion.body_slam"}, "spawn_egg": {"base_color": "#a57892", "overlay_color": "#9b2794"}}}}