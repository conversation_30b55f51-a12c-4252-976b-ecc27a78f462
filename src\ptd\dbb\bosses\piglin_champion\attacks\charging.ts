import { Entity, Entity<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Player, Vector3 } from "@minecraft/server";
import { getDistance } from "../../../utilities/vector3";
import { PIGLIN_CHAMPION_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";

/**
 * Executes the charging attack for the Piglin Champion
 * Applies damage and knockback to nearby entities when the piglin charges into them
 * After hitting a player, the piglin will be stunned for 6 seconds (120 ticks)
 *
 * @param piglinChampion The piglin champion entity
 */
export function executeChargingAttack(piglinChampion: Entity): void {
  // Apply damage to nearby entities
  const damageRadius = 4;
  // Use direct damage value instead of percentage
  const damage = PIGLIN_CHAMPION_ATTACK_DAMAGES.charging.damage;

  // Get direction vector directly from the piglin's view direction
  const viewDirection = piglinChampion.getViewDirection();
  const dirX = viewDirection.x;
  const dirZ = viewDirection.z;

  // Calculate position 3 blocks in front of the piglin
  const originPos: Vector3 = {
    x: piglinChampion.location.x + dirX * 3,
    y: piglinChampion.location.y,
    z: piglinChampion.location.z + dirZ * 3
  };

  // Apply damage to nearby entities
  piglinChampion.dimension
    .getEntities({
      location: originPos,
      maxDistance: damageRadius,
      excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
      excludeFamilies: ["piglin_champion", "piglin", "rock"]
    })
    .forEach((entity) => {
      // Apply damage
      entity.applyDamage(damage, { cause: EntityDamageCause.entityAttack, damagingEntity: piglinChampion });

      // Use piglin's direction for knockback
      // Create 2D points (same y-coordinate) to calculate horizontal distance
      const point1: Vector3 = { x: entity.location.x, y: 0, z: entity.location.z };
      const point2: Vector3 = { x: originPos.x, y: 0, z: originPos.z };
      const distance = getDistance(point1, point2);

      if (distance > 0) {
        // Use the piglin's direction for knockback
        const nx = dirX;
        const nz = dirZ;

        // Charging attack parameters
        const horizontalStrength = 6.0; // Knock players back 6 blocks
        const verticalStrength = 0.8;

        try {
          // Try to apply knockback first
          if (entity instanceof Player) {
            const gameMode = entity.getGameMode();
            if (gameMode === GameMode.survival || gameMode === GameMode.adventure) {
              entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
            }
          } else {
            entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
          }
        } catch (e) {
          // Fallback to applyImpulse if applyKnockback fails
          const impulse: Vector3 = {
            x: nx * horizontalStrength,
            y: verticalStrength,
            z: nz * horizontalStrength
          };

          entity.applyImpulse(impulse);
        }
      }
    });
}
